{% extends "base.html" %}

{% block title %}影片详情 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <div class="mb-3">
                    <a href="/movies" class="btn btn-outline btn-sm">
                        <i class="bi bi-arrow-left" aria-label="返回图标"></i>
                        返回影片库
                    </a>
                </div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3" id="movie-title">
                    <i class="bi bi-film text-primary" style="font-size: 2rem;" aria-label="影片详情图标"></i>
                    影片详情
                </h1>
                <p class="text-base-content/70 mt-2">查看和管理影片信息</p>
            </div>
            <div class="flex items-center gap-3">
                <!-- 收藏按钮 -->
                <div id="favorite-btn-container">
                    <!-- 收藏按钮将在这里动态生成 -->
                </div>
                <button type="button" class="btn btn-primary btn-sm hidden" id="edit-btn">
                    <i class="bi bi-pencil" aria-label="编辑图标"></i>
                    编辑影片
                </button>
                <button type="button" class="btn btn-info btn-outline btn-sm hidden" id="sync-nfo-btn" title="将数据库信息同步到NFO文件">
                    <i class="bi bi-arrow-repeat" aria-label="同步图标"></i>
                    同步到NFO
                </button>
                <button type="button" class="btn btn-outline btn-sm" id="refresh-btn">
                    <i class="bi bi-arrow-clockwise" aria-label="刷新图标"></i>
                    刷新
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 页面内容 -->
<div class="container mx-auto px-4 py-6">
    <!-- 加载状态 -->
    <div id="loading-container" class="flex flex-col items-center justify-center py-16">
        <span class="loading loading-spinner loading-lg text-primary"></span>
        <div class="mt-4 text-base-content">正在加载影片详情...</div>
    </div>

    <!-- 错误状态 -->
    <div id="error-container" class="alert alert-error hidden">
        <i class="bi bi-exclamation-circle stroke-current shrink-0" style="font-size: 1.5rem;" aria-label="错误图标"></i>
        <div>
            <h3 class="font-bold">加载失败</h3>
            <div class="text-xs" id="error-message">无法加载影片详情，请稍后重试。</div>
        </div>
    </div>

    <!-- 影片详情内容 -->
    <div id="movie-detail-container" class="hidden">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- 左侧：海报图片 -->
            <div class="lg:col-span-1">
                <div class="card bg-base-100 shadow-lg">
                    <figure class="p-0">
                        <img id="movie-poster" src="" alt="" class="w-full aspect-[2/3] object-cover rounded-2xl">
                    </figure>
                </div>
            </div>

            <!-- 右侧：详细信息 -->
            <div class="lg:col-span-3">
                <div class="card bg-base-100 shadow-lg">
                    <div class="card-body">
                        <!-- 标题部分 -->
                        <div class="mb-6">
                            <h2 class="card-title text-2xl font-bold mb-2" id="detail-movie-title">影片标题</h2>
                            <div class="text-base-content/70 text-lg" id="movie-original-title"></div>
                        </div>

                        <!-- 基本信息 -->
                        <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                            <div class="stat bg-base-200 rounded-lg p-4">
                                <div class="stat-title text-sm">年份</div>
                                <div class="stat-value text-lg" id="movie-year">-</div>
                            </div>
                            <div class="stat bg-base-200 rounded-lg p-4">
                                <div class="stat-title text-sm">评分</div>
                                <div class="stat-value text-lg" id="movie-rating">-</div>
                            </div>
                            <div class="stat bg-base-200 rounded-lg p-4">
                                <div class="stat-title text-sm">时长</div>
                                <div class="stat-value text-lg" id="movie-runtime">-</div>
                            </div>
                            <div class="stat bg-base-200 rounded-lg p-4">
                                <div class="stat-title text-sm">国家/地区</div>
                                <div class="stat-value text-lg" id="movie-country">-</div>
                            </div>
                        </div>

                        <!-- 分类和系列 -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="text-lg font-semibold mb-3 text-base-content">分类</h3>
                                <div id="movie-genres" class="flex flex-wrap gap-2">
                                    <!-- 分类标签将在这里动态生成 -->
                                </div>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold mb-3 text-base-content">系列</h3>
                                <div id="movie-series" class="text-base-content/80">-</div>
                            </div>
                        </div>

                        <!-- 标签 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-3 text-base-content">标签</h3>
                            <div id="movie-tags" class="flex flex-wrap gap-2">
                                <!-- 标签将在这里动态生成 -->
                            </div>
                        </div>

                        <!-- 演员 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-3 text-base-content">演员</h3>
                            <div id="movie-actors" class="flex flex-wrap gap-2">
                                <!-- 演员列表将在这里动态生成 -->
                            </div>
                        </div>

                        <!-- 剧情简介 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-3 text-base-content">剧情简介</h3>
                            <div id="movie-plot" class="text-base-content/80 leading-relaxed">
                                暂无剧情简介
                            </div>
                        </div>

                        <!-- 文件信息 -->
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-3 text-base-content">文件路径</h3>
                            <div id="movie-file-path" class="text-base-content/70 font-mono text-sm bg-base-200 p-3 rounded-lg break-all">
                                -
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- NFO同步确认对话框 -->
<dialog id="nfo-sync-modal" class="modal bg-base-100/80 backdrop-blur-sm">
    <div class="modal-box w-11/12 max-w-lg bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4 flex items-center">
            <i class="bi bi-arrow-repeat mr-2" style="font-size: 1.5rem;" aria-label="同步图标"></i>
            同步到NFO文件
        </h3>

        <div id="nfo-sync-content">
            <div class="flex justify-center">
                <span class="loading loading-spinner loading-lg text-primary"></span>
            </div>
            <p class="text-center mt-4">正在检查NFO文件状态...</p>
        </div>

        <div class="modal-action hidden" id="nfo-sync-footer">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('nfo-sync-modal').close()">取消</button>
            <button type="button" class="btn btn-primary" id="confirm-sync-btn">
                <i class="bi bi-check mr-2" aria-label="确认图标"></i>
                确认同步
            </button>
        </div>
    </div>
</dialog>

<!-- NFO同步结果对话框 -->
<dialog id="nfo-result-modal" class="modal bg-base-100/80 backdrop-blur-sm">
    <div class="modal-box w-11/12 max-w-lg bg-base-100/80 backdrop-blur-sm">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">同步结果</h3>

        <div id="nfo-result-content">
            <!-- 结果内容将在这里动态生成 -->
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-primary" onclick="document.getElementById('nfo-result-modal').close()">确定</button>
        </div>
    </div>
</dialog>


{% endblock %}


{% block extra_js %}
<script src="/static/js/favorite-manager.js?=v0.9.16"></script>
<script src="/static/js/movie-detail.js?=v0.9.16"></script>
<script>
// 初始化电影详情页面
document.addEventListener('DOMContentLoaded', function() {
    // 获取访问类型和参数
    const accessType = '{{ access_type }}';
    const movieId = {{ movie_id }};

    // 初始化电影详情管理器
    if (window.initMovieDetail) {
        window.initMovieDetail(accessType, movieId);
    }
});
</script>
{% endblock %}
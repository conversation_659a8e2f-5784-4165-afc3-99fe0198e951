"""
分类管理 API 路由
提供分类的 CRUD 操作接口
"""
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.services.management_service import GenreService
from app.schemas.schemas import (
    GenreCreate, GenreUpdate, GenreResponse, GenreListResponse, GenreListRequest, BaseResponse,
    BatchDeleteRequest, BatchDeleteResponse, UnifiedDeleteRequest
)
import logging

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    prefix="/genres",
    tags=["分类管理"],
    responses={404: {"description": "分类不存在"}}
)


@router.post("/add", response_model=BaseResponse)
async def create_genre(genre_data: GenreCreate, db: Session = Depends(get_db)):
    """
    创建新分类
    
    Args:
        genre_data: 分类创建数据
        db: 数据库会话
        
    Returns:
        创建结果
    """
    try:
        genre_service = GenreService(db)
        genre = genre_service.create_genre(genre_data)
        
        if not genre:
            raise HTTPException(status_code=400, detail="分类名称已存在")
        
        return BaseResponse(
            success=True,
            message="分类创建成功",
            data=GenreResponse.model_validate(genre)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建分类时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"创建分类失败: {str(e)}")


@router.post("/list", response_model=GenreListResponse)
async def get_genres(request: GenreListRequest, db: Session = Depends(get_db)):
    """
    获取分类列表

    Args:
        request: 分类列表请求参数
        db: 数据库会话

    Returns:
        分类列表
    """
    try:
        # 参数验证
        if request.limit < 1 or request.limit > 1000:
            raise HTTPException(status_code=400, detail="limit 必须在 1-1000 之间")
        if request.offset < 0:
            raise HTTPException(status_code=400, detail="offset 必须大于等于 0")

        genre_service = GenreService(db)
        genres, total_count = genre_service.get_genres(
            limit=request.limit,
            offset=request.offset,
            search=request.search
        )

        # 为每个分类添加电影数量
        genre_responses = []
        for genre in genres:
            genre_info = genre_service.get_genre_with_movie_count(genre.id)
            genre_response = GenreResponse.model_validate(genre)
            genre_response.movie_count = genre_info["movie_count"] if genre_info else 0
            genre_responses.append(genre_response)

        return GenreListResponse(
            success=True,
            message=f"获取到 {len(genres)} 个分类",
            data=genre_responses,
            total_count=total_count,
            limit=request.limit,
            offset=request.offset
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分类列表时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取分类列表失败: {str(e)}")


@router.get("/info/{genre_id}", response_model=BaseResponse)
async def get_genre(genre_id: int, db: Session = Depends(get_db)):
    """
    获取指定分类详情
    
    Args:
        genre_id: 分类 ID
        db: 数据库会话
        
    Returns:
        分类详情
    """
    try:
        genre_service = GenreService(db)
        genre_info = genre_service.get_genre_with_movie_count(genre_id)
        
        if not genre_info:
            raise HTTPException(status_code=404, detail="分类不存在")
        
        genre_response = GenreResponse.model_validate(genre_info["genre"])
        genre_response.movie_count = genre_info["movie_count"]
        
        return BaseResponse(
            success=True,
            message="获取分类详情成功",
            data=genre_response
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取分类详情时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取分类详情失败: {str(e)}")


@router.put("/edit/{genre_id}", response_model=BaseResponse)
async def update_genre(genre_id: int, genre_data: GenreUpdate, db: Session = Depends(get_db)):
    """
    更新分类信息
    
    Args:
        genre_id: 分类 ID
        genre_data: 更新数据
        db: 数据库会话
        
    Returns:
        更新结果
    """
    try:
        genre_service = GenreService(db)
        genre = genre_service.update_genre(genre_id, genre_data)
        
        if not genre:
            # 检查是否是因为分类不存在
            existing_genre = genre_service.get_genre_by_id(genre_id)
            if not existing_genre:
                raise HTTPException(status_code=404, detail="分类不存在")
            else:
                raise HTTPException(status_code=400, detail="分类名称已存在")
        
        return BaseResponse(
            success=True,
            message="分类更新成功",
            data=GenreResponse.model_validate(genre)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新分类时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"更新分类失败: {str(e)}")


@router.delete("", response_model=BaseResponse)
async def delete_genres(request: UnifiedDeleteRequest, db: Session = Depends(get_db)):
    """
    统一删除分类（支持单项和批量删除）

    Args:
        request: 统一删除请求，支持单个ID或ID列表
        db: 数据库会话

    Returns:
        删除结果
    """
    try:
        genre_service = GenreService(db)
        result = genre_service.unified_delete_genres(request)

        if isinstance(result, tuple):
            # 单项删除结果
            success, message = result
            if not success:
                if "不存在" in message:
                    raise HTTPException(status_code=404, detail=message)
                else:
                    raise HTTPException(status_code=400, detail=message)

            return BaseResponse(
                success=True,
                message=message
            )
        else:
            # 批量删除结果
            batch_result = result
            if batch_result.failed_count == 0:
                message = f"成功删除 {batch_result.success_count} 个分类"
            elif batch_result.success_count == 0:
                message = f"删除失败，{batch_result.failed_count} 个分类无法删除"
            else:
                message = f"成功删除 {batch_result.success_count} 个分类，{batch_result.failed_count} 个失败"

            return BaseResponse(
                success=True,
                message=message,
                data=batch_result
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除分类时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除分类失败: {str(e)}")

/**
 * API 客户端模块
 * 处理与后端 API 的所有交互
 */

class ApiClient {
    constructor() {
        this.baseURL = window.location.origin + '/api';
        this.defaultHeaders = {
            'Content-Type': 'application/json',
        };
        this.timeout = 30000; // 30秒超时
        this.retries = 3;
    }

    /**
     * 发送 HTTP 请求
     * @param {string} endpoint - API 端点
     * @param {Object} options - 请求选项
     * @returns {Promise} 响应数据
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        const config = {
            headers: { ...this.defaultHeaders, ...options.headers },
            signal: AbortSignal.timeout(this.timeout),
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error(`API 请求失败 [${endpoint}]:`, error);
            throw error;
        }
    }

    /**
     * 带重试的请求
     * @param {Function} requestFn - 请求函数
     * @param {number} maxRetries - 最大重试次数
     * @returns {Promise} 响应数据
     */
    async requestWithRetry(requestFn, maxRetries = this.retries) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                return await requestFn();
            } catch (error) {
                if (i === maxRetries - 1) throw error;
                
                const delay = Math.pow(2, i) * 1000; // 指数退避
                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }
    }

    /**
     * GET 请求
     * @param {string} endpoint - API 端点
     * @param {Object} params - 查询参数
     * @returns {Promise} 响应数据
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        return this.request(url, { method: 'GET' });
    }

    /**
     * POST 请求
     * @param {string} endpoint - API 端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 响应数据
     */
    async post(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT 请求
     * @param {string} endpoint - API 端点
     * @param {Object} data - 请求数据
     * @returns {Promise} 响应数据
     */
    async put(endpoint, data = {}) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE 请求
     * @param {string} endpoint - API 端点
     * @param {Object} data - 请求数据（可选）
     * @returns {Promise} 响应数据
     */
    async delete(endpoint, data = null) {
        const options = { method: 'DELETE' };
        if (data) {
            options.body = JSON.stringify(data);
        }
        return this.request(endpoint, options);
    }

    // ========== 影片相关 API ==========

    /**
     * 获取影片列表
     * @param {Object} params - 过滤参数
     * @returns {Promise} 影片列表数据
     */
    async getMovies(params = {}) {
        return this.post('/movies/list', params);
    }

    /**
     * 获取影片详情
     * @param {number} movieId - 影片 ID
     * @returns {Promise} 影片详情数据
     */
    async getMovie(movieId) {
        return this.get(`/movies/info/${movieId}`);
    }

    /**
     * 更新影片信息
     * @param {number} movieId - 影片 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateMovie(movieId, data) {
        return this.put(`/movies/edit/${movieId}`, data);
    }

    /**
     * 删除影片（批量删除）
     * @param {number|Array<number>} movieIds - 影片 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteMovie(movieIds, force = false) {
        // 统一处理为数组格式
        const ids = Array.isArray(movieIds) ? movieIds : [movieIds];
        return this.delete('/movies', {
            movies_ids: ids,
            force: force
        });
    }

    /**
     * 批量删除影片
     * @param {Array<number>} movieIds - 影片 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteMovies(movieIds, force = false) {
        return this.deleteMovie(movieIds, force);
    }

    /**
     * 获取影片NFO文件状态
     * @param {number} movieId - 影片 ID
     * @returns {Promise} NFO状态信息
     */
    async getNFOStatus(movieId) {
        return this.get(`/movies/nfo-status/${movieId}`);
    }

    /**
     * 同步影片NFO文件
     * @param {number} movieId - 影片 ID
     * @returns {Promise} 同步结果
     */
    async syncMovieNFO(movieId) {
        return this.post(`/movies/sync-nfo/${movieId}`);
    }

    // ========== 标签相关 API ==========

    /**
     * 获取标签列表
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @param {string} search - 搜索关键词
     * @returns {Promise} 标签列表数据
     */
    async getTags(limit = 50, offset = 0, search = null) {
        return this.post('/tags/list', {
            limit,
            offset,
            search
        });
    }

    /**
     * 创建标签
     * @param {Object} data - 标签数据
     * @returns {Promise} 创建结果
     */
    async createTag(data) {
        return this.post('/tags/add', data);
    }

    /**
     * 更新标签
     * @param {number} tagId - 标签 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateTag(tagId, data) {
        return this.put(`/tags/edit/${tagId}`, data);
    }

    /**
     * 删除标签
     * @param {number|Array<number>} ids - 标签 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteTag(ids, force = false) {
        return this.delete('/tags', {
            ids: ids,
            force: force
        });
    }

    // ========== 分类相关 API ==========

    /**
     * 获取分类列表
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @param {string} search - 搜索关键词
     * @returns {Promise} 分类列表数据
     */
    async getGenres(limit = 50, offset = 0, search = null) {
        return this.post('/genres/list', {
            limit,
            offset,
            search
        });
    }

    /**
     * 创建分类
     * @param {Object} data - 分类数据
     * @returns {Promise} 创建结果
     */
    async createGenre(data) {
        return this.post('/genres/add', data);
    }

    /**
     * 更新分类
     * @param {number} genreId - 分类 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateGenre(genreId, data) {
        return this.put(`/genres/edit/${genreId}`, data);
    }

    /**
     * 删除分类
     * @param {number|Array<number>} ids - 分类 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteGenre(ids, force = false) {
        return this.delete('/genres', {
            ids: ids,
            force: force
        });
    }

    // ========== 系列相关 API ==========

    /**
     * 获取系列列表
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @param {string} search - 搜索关键词
     * @returns {Promise} 系列列表数据
     */
    async getSeries(limit = 50, offset = 0, search = null) {
        return this.post('/series/list', {
            limit,
            offset,
            search
        });
    }

    /**
     * 创建系列
     * @param {Object} data - 系列数据
     * @returns {Promise} 创建结果
     */
    async createSeries(data) {
        return this.post('/series/add', data);
    }

    /**
     * 更新系列
     * @param {number} seriesId - 系列 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateSeries(seriesId, data) {
        return this.put(`/series/edit/${seriesId}`, data);
    }

    /**
     * 删除系列
     * @param {number|Array<number>} ids - 系列 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteSeries(ids, force = false) {
        return this.delete('/series', {
            ids: ids,
            force: force
        });
    }

    // ========== 演员相关 API ==========

    /**
     * 获取演员列表
     * @param {number} limit - 每页数量
     * @param {number} offset - 偏移量
     * @param {string} search - 搜索关键词
     * @returns {Promise} 演员列表数据
     */
    async getActors(limit = 50, offset = 0, search = null) {
        return this.post('/actors/list', {
            limit,
            offset,
            search
        });
    }

    /**
     * 创建演员
     * @param {Object} data - 演员数据
     * @returns {Promise} 创建结果
     */
    async createActor(data) {
        return this.post('/actors/add', data);
    }

    /**
     * 更新演员
     * @param {number} actorId - 演员 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateActor(actorId, data) {
        return this.put(`/actors/edit/${actorId}`, data);
    }

    /**
     * 删除演员
     * @param {number|Array<number>} ids - 演员 ID 或 ID 列表
     * @param {boolean} force - 是否强制删除
     * @returns {Promise} 删除结果
     */
    async deleteActors(ids, force = false) {
        return this.delete('/actors', {
            ids: ids,
            force: force
        });
    }

    // ========== 目录相关 API ==========

    /**
     * 获取目录列表
     * @param {boolean} enabledOnly - 是否只获取启用的目录
     * @param {boolean} withCover - 是否包含封面图片
     * @returns {Promise} 目录列表数据
     */
    async getDirectories(enabledOnly = false, withCover = false) {
        const requestData = {
            enabled_only: enabledOnly,
            with_cover: withCover
        };
        return this.post('/directories/list', requestData);
    }

    /**
     * 创建目录
     * @param {Object} data - 目录数据
     * @returns {Promise} 创建结果
     */
    async createDirectory(data) {
        return this.post('/directories/add', data);
    }

    /**
     * 更新目录
     * @param {number} directoryId - 目录 ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateDirectory(directoryId, data) {
        return this.put(`/directories/edit/${directoryId}`, data);
    }

    /**
     * 创建目录（支持多路径）
     * @param {Object} data - 目录数据，包含paths数组
     * @returns {Promise} 创建结果
     */
    async createDirectoryWithPaths(data) {
        return this.post('/directories/add', data);
    }

    /**
     * 更新目录（支持多路径）
     * @param {number} directoryId - 目录 ID
     * @param {Object} data - 更新数据，包含paths数组
     * @returns {Promise} 更新结果
     */
    async updateDirectoryWithPaths(directoryId, data) {
        return this.put(`/directories/edit/${directoryId}`, data);
    }

    /**
     * 获取目录详情
     * @param {number} directoryId - 目录 ID
     * @returns {Promise} 目录详情
     */
    async getDirectoryInfo(directoryId) {
        return this.get(`/directories/info/${directoryId}`);
    }

    /**
     * 删除目录（支持批量删除）
     * @param {number|Array<number>} directoryIds - 目录 ID 或 ID 列表
     * @returns {Promise} 删除结果
     */
    async deleteDirectories(directoryIds) {
        // 统一处理为数组格式
        const ids = Array.isArray(directoryIds) ? directoryIds : [directoryIds];
        const requestData = {
            directories_ids: ids
        };
        return this.delete('/directories', requestData);
    }

    /**
     * 删除单个目录（兼容性方法）
     * @param {number} directoryId - 目录 ID
     * @returns {Promise} 删除结果
     */
    async deleteDirectory(directoryId) {
        return this.deleteDirectories(directoryId);
    }

    /**
     * 扫描目录（增量扫描）
     * @param {number} directoryId - 目录 ID
     * @returns {Promise} 扫描任务信息
     */
    async scanDirectory(directoryId) {
        return this.post('/directories/scan', {
            directories_ids: [directoryId],
            incremental: true
        });
    }

    /**
     * 全量扫描目录
     * @param {number} directoryId - 目录 ID
     * @returns {Promise} 扫描任务信息
     */
    async fullScanDirectory(directoryId) {
        return this.post('/directories/scan', {
            directories_ids: [directoryId],
            incremental: false
        });
    }

    /**
     * 批量扫描指定目录
     * @param {number[]} directoryIds - 目录 ID 列表
     * @param {boolean} incremental - 是否增量扫描（默认true）
     * @returns {Promise} 扫描任务信息
     */
    async scanDirectories(directoryIds, incremental = true) {
        return this.post('/directories/scan', {
            directories_ids: directoryIds,
            incremental: incremental
        });
    }

    /**
     * 扫描所有启用的目录
     * @param {boolean} incremental - 是否增量扫描（默认true）
     * @returns {Promise} 扫描任务信息
     */
    async scanAllDirectories(incremental = true) {
        return this.post('/directories/scan', {
            directories_ids: [],
            incremental: incremental
        });
    }

    /**
     * 获取扫描进度
     * @param {string} taskId - 任务 ID
     * @returns {Promise} 扫描进度信息
     */
    async getScanProgress(taskId) {
        return this.get(`/directories/scan/progress/${taskId}`);
    }

    /**
     * 暂停扫描任务
     * @param {string} taskId - 任务 ID
     * @returns {Promise} 操作结果
     */
    async pauseScan(taskId) {
        return this.post(`/directories/scan/pause/${taskId}`);
    }

    /**
     * 恢复扫描任务
     * @param {string} taskId - 任务 ID
     * @returns {Promise} 操作结果
     */
    async resumeScan(taskId) {
        return this.post(`/directories/scan/resume/${taskId}`);
    }

    /**
     * 取消扫描任务
     * @param {string} taskId - 任务 ID
     * @returns {Promise} 操作结果
     */
    async cancelScan(taskId) {
        return this.post(`/directories/scan/cancel/${taskId}`);
    }

    /**
     * 获取活跃的扫描任务
     * @returns {Promise} 活跃任务列表
     */
    async getActiveScans() {
        return this.get('/directories/scan/active');
    }

    /**
     * 重新生成所有目录封面
     * @returns {Promise} 生成结果
     */
    async regenerateAllCovers() {
        return this.post('/directories/regenerate-all-covers');
    }

    /**
     * 浏览目录
     * @param {string} path - 目录路径（可选）
     * @returns {Promise} 目录浏览结果
     */
    async browseDirectory(path = null) {
        const requestBody = {};
        if (path) {
            requestBody.path = path;
        }
        return this.post('/directories/browse', requestBody);
    }

    // ========== 图片相关 API ==========

    /**
     * 获取图片 URL
     * @param {string} imageUuid - 图片 UUID
     * @returns {string} 图片 URL
     */
    getImageUrl(imageUuid) {
        if (!imageUuid) return null;
        return `${this.baseURL}/images/${imageUuid}`;
    }

    /**
     * 获取图片信息
     * @param {string} imageUuid - 图片 UUID
     * @returns {Promise} 图片信息
     */
    async getImageInfo(imageUuid) {
        return this.get(`/images/info/${imageUuid}`);
    }

    /**
     * 获取图片列表
     * @param {Object} params - 查询参数
     * @param {string} params.image_type - 图片类型过滤（可选）
     * @param {number} params.limit - 返回数量限制（默认50）
     * @param {number} params.offset - 偏移量（默认0）
     * @returns {Promise} 图片列表
     */
    async getImageList(params = {}) {
        const requestData = {
            image_type: params.image_type || null,
            limit: params.limit || 50,
            offset: params.offset || 0
        };
        return this.post('/images/list', requestData);
    }

    // ========== 系统相关 API ==========

    /**
     * 获取数据库统计信息
     * @returns {Promise} 统计数据
     */
    async getDatabaseStats() {
        return this.get('/database/stats');
    }

    /**
     * 健康检查
     * @returns {Promise} 健康状态
     */
    async healthCheck() {
        return this.get('/health');
    }

    // ========== 收藏功能 API ==========

    /**
     * 添加收藏影片（统一接口）
     * @param {number|number[]} movieIds - 影片ID或影片ID列表
     * @returns {Promise} 添加收藏结果
     */
    async addFavorites(movieIds) {
        // 确保 movieIds 是数组格式
        const ids = Array.isArray(movieIds) ? movieIds : [movieIds];

        return this.request('/favorites/add', {
            method: 'POST',
            body: JSON.stringify({ movie_ids: ids })
        });
    }

    /**
     * 收藏影片（兼容性方法）
     * @param {number} movieId - 影片ID
     * @returns {Promise} 收藏结果
     * @deprecated 请使用 addFavorites 方法
     */
    async addFavorite(movieId) {
        return this.addFavorites(movieId);
    }

    /**
     * 取消收藏影片
     * @param {number} movieId - 影片ID
     * @returns {Promise} 取消收藏结果
     */
    async removeFavorite(movieId) {
        return this.request('/favorites', {
            method: 'DELETE',
            body: JSON.stringify({ movie_ids: [movieId] })
        });
    }

    /**
     * 切换收藏状态
     * @param {number} movieId - 影片ID
     * @returns {Promise} 切换结果
     */
    async toggleFavorite(movieId) {
        return this.request(`/favorites/${movieId}/toggle`, {
            method: 'PUT'
        });
    }

    /**
     * 获取收藏状态
     * @param {number} movieId - 影片ID
     * @returns {Promise} 收藏状态
     */
    async getFavoriteStatus(movieId) {
        return this.request(`/favorites/${movieId}/status`);
    }

    /**
     * 获取收藏列表
     * @param {Object} filterParams - 过滤参数
     * @returns {Promise} 收藏列表
     */
    async getFavorites(filterParams = {}) {
        return this.request('/favorites/list', {
            method: 'POST',
            body: JSON.stringify(filterParams)
        });
    }

    /**
     * 批量收藏影片（兼容性方法）
     * @param {number[]} movieIds - 影片ID列表
     * @returns {Promise} 批量收藏结果
     * @deprecated 请使用 addFavorites 方法
     */
    async batchAddFavorites(movieIds) {
        return this.addFavorites(movieIds);
    }

    /**
     * 批量移除收藏影片
     * @param {number[]} movieIds - 影片ID列表
     * @returns {Promise} 批量移除收藏结果
     */
    async batchRemoveFavorites(movieIds) {
        return this.request('/favorites', {
            method: 'DELETE',
            body: JSON.stringify({ movie_ids: movieIds })
        });
    }

    /**
     * 批量同步NFO文件
     * @param {number[]} movieIds - 影片ID列表
     * @returns {Promise} 批量同步NFO结果
     */
    async batchSyncNfo(movieIds) {
        return this.request('/movies/batch/sync-nfo', {
            method: 'POST',
            body: JSON.stringify({ movie_ids: movieIds })
        });
    }

    // ========== 配置管理 API ==========

    /**
     * 获取指定配置项
     * @param {string} configKey - 配置键名
     * @returns {Promise} 配置项信息
     */
    async getConfig(configKey) {
        return this.request(`/configs/${configKey}`);
    }

    /**
     * 创建配置项
     * @param {Object} configData - 配置数据
     * @returns {Promise} 创建结果
     */
    async createConfig(configData) {
        return this.request('/configs/', {
            method: 'POST',
            body: JSON.stringify(configData)
        });
    }

    /**
     * 更新配置项
     * @param {string} configKey - 配置键名
     * @param {Object} configData - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateConfig(configKey, configData) {
        return this.request(`/configs/${configKey}`, {
            method: 'PUT',
            body: JSON.stringify(configData)
        });
    }

    /**
     * 删除配置项
     * @param {string} configKey - 配置键名
     * @returns {Promise} 删除结果
     */
    async deleteConfig(configKey) {
        return this.request(`/configs/${configKey}`, {
            method: 'DELETE'
        });
    }

    /**
     * 获取所有配置项
     * @returns {Promise} 配置项列表
     */
    async getAllConfigs() {
        return this.request('/configs/');
    }

    /**
     * 获取当前视频扩展名配置
     * @returns {Promise} 视频扩展名配置
     */
    async getVideoExtensions() {
        return this.request('/configs/video-extensions/current');
    }

    /**
     * 更新视频扩展名配置
     * @param {Array} extensions - 扩展名数组
     * @returns {Promise} 更新结果
     */
    async updateVideoExtensions(extensions) {
        return this.request('/configs/video-extensions/update', {
            method: 'POST',
            body: JSON.stringify({ extensions })
        });
    }



    // ========== 映射管理 API ==========

    /**
     * 获取映射规则列表
     * @param {string} mappingType - 映射类型 (tags/genres/actors)
     * @param {Object} params - 查询参数
     * @returns {Promise} 映射规则列表
     */
    async getMappingRules(mappingType, params = {}) {
        // 设置默认值
        const requestData = {
            limit: params.limit || 50,
            offset: params.offset || 0,
            search: params.search || null,
            status: params.status || null
        };

        return this.request(`/mappings/${mappingType}/list`, {
            method: 'POST',
            body: JSON.stringify(requestData)
        });
    }

    /**
     * 创建映射规则
     * @param {string} mappingType - 映射类型
     * @param {Object} data - 映射规则数据
     * @returns {Promise} 创建结果
     */
    async createMappingRule(mappingType, data) {
        return this.request(`/mappings/${mappingType}/add`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * 更新映射规则
     * @param {string} mappingType - 映射类型
     * @param {number} ruleId - 规则ID
     * @param {Object} data - 更新数据
     * @returns {Promise} 更新结果
     */
    async updateMappingRule(mappingType, ruleId, data) {
        return this.request(`/mappings/${mappingType}/edit/${ruleId}`, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * 删除映射规则（批量）
     * @param {string} mappingType - 映射类型
     * @param {Array<number>} ruleIds - 规则ID列表
     * @returns {Promise} 删除结果
     */
    async deleteMappingRules(mappingType, ruleIds) {
        return this.request(`/mappings/${mappingType}`, {
            method: 'DELETE',
            body: JSON.stringify({
                mappings_ids: Array.isArray(ruleIds) ? ruleIds : [ruleIds]
            })
        });
    }

    /**
     * 删除单个映射规则（兼容性方法）
     * @param {string} mappingType - 映射类型
     * @param {number} ruleId - 规则ID
     * @returns {Promise} 删除结果
     */
    async deleteMappingRule(mappingType, ruleId) {
        return this.deleteMappingRules(mappingType, [ruleId]);
    }

    /**
     * 导入映射规则
     * @param {string} mappingType - 映射类型
     * @param {Object} data - 导入数据
     * @returns {Promise} 导入结果
     */
    async importMappingRules(mappingType, data) {
        return this.request(`/mappings/${mappingType}/import`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * 导出映射规则
     * @param {string} mappingType - 映射类型
     * @returns {Promise} 导出数据
     */
    async exportMappingRules(mappingType) {
        return this.request(`/mappings/${mappingType}/export`);
    }
}

// 创建全局 API 客户端实例
const api = new ApiClient();

// 错误处理工具函数
const handleApiError = (error, context = '', showToast = true) => {
    console.error(`API 错误 ${context}:`, error);

    let message = '操作失败，请稍后重试';

    if (error.message) {
        if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
            message = '网络连接失败，请检查网络连接';
        } else if (error.message.includes('404')) {
            message = '请求的资源不存在';
        } else if (error.message.includes('500')) {
            message = '服务器内部错误';
        } else if (error.message.includes('timeout')) {
            message = '请求超时，请稍后重试';
        } else {
            message = error.message;
        }
    }

    // 自动显示错误Toast（除非明确禁用）
    if (showToast && window.toast) {
        window.toast.error(message);
    }

    return message;
};

// 导出 API 客户端和工具函数
window.api = api;
window.handleApiError = handleApiError;

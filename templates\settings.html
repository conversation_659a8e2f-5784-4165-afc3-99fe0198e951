{% extends "base.html" %}

{% block title %}系统设置 - 媒体管理器{% endblock %}

{% block page_header %}
<!-- 页面标题区域 -->
<div class="bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-base-300">
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
                <h1 class="text-3xl font-bold text-base-content flex items-center gap-3">
                    <i class="bi bi-gear text-primary" style="font-size: 2rem;" aria-label="设置图标"></i>
                    系统设置
                </h1>
                <p class="text-base-content/70 mt-2">配置系统参数和映射规则</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- 设置导航标签 -->
<div class="tabs tabs-boxed mb-6 bg-base-100 shadow-lg border border-base-300 p-2">
    <a class="tab tab-active" data-tab="mappings">
        <i class="bi bi-arrow-left-right mr-2" aria-label="映射图标"></i>
        映射配置
    </a>
    <a class="tab" data-tab="general">
        <i class="bi bi-sliders mr-2" aria-label="常规图标"></i>
        常规设置
    </a>
    <a class="tab" data-tab="display">
        <i class="bi bi-display mr-2" aria-label="显示图标"></i>
        显示设置
    </a>
    <a class="tab" data-tab="about">
        <i class="bi bi-info-circle mr-2" aria-label="关于图标"></i>
        关于系统
    </a>
</div>

<!-- 映射配置面板 -->
<div id="mappings-panel" class="tab-panel">
    <!-- 映射类型选择 -->
    <div class="card bg-base-100 shadow-lg border border-base-300 mb-6">
        <div class="card-body">
            <h3 class="card-title text-xl mb-4">
                <i class="bi bi-arrow-left-right text-primary" aria-label="映射图标"></i>
                映射规则管理
            </h3>
            <p class="text-base-content/70 mb-6">配置标签、分类和演员的映射规则，用于数据导入时的自动转换</p>
            
            <!-- 映射类型选择器 -->
            <div class="flex flex-wrap gap-3 mb-6">
                <button class="btn btn-outline btn-active" data-mapping-type="tags">
                    <i class="bi bi-tags mr-2" aria-label="标签图标"></i>
                    <span class="hidden sm:inline">标签映射</span>
                </button>
                <button class="btn btn-outline" data-mapping-type="genres">
                    <i class="bi bi-collection mr-2" aria-label="分类图标"></i>
                    <span class="hidden sm:inline">分类映射</span>
                </button>
                <button class="btn btn-outline" data-mapping-type="actors">
                    <i class="bi bi-people mr-2" aria-label="演员图标"></i>
                    <span class="hidden sm:inline">演员映射</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 映射操作区域 -->
    <div class="card bg-base-100 shadow-lg border border-base-300 mb-4 md:mb-6">
        <div class="card-body p-3 md:p-6">
            <div class="flex flex-col md:flex-row gap-3 md:gap-4 items-stretch md:items-end">
                <!-- 搜索输入框 -->
                <div class="form-control flex-1">
                    <label class="label py-1 md:py-2">
                        <span class="label-text font-medium text-sm md:text-base">搜索映射规则</span>
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="bi bi-search text-base-content/40" aria-label="搜索图标"></i>
                        </div>
                        <input type="text" class="input input-bordered w-full pl-10 input-sm md:input-md" id="mapping-search-input" placeholder="搜索原始值或映射值...">
                    </div>
                </div>

                <!-- 状态过滤 -->
                <div class="form-control w-full md:w-auto">
                    <label class="label py-1 md:py-2">
                        <span class="label-text font-medium text-sm md:text-base">状态过滤</span>
                    </label>
                    <select class="select select-bordered select-sm md:select-md" id="mapping-status-filter">
                        <option value="">全部状态</option>
                        <option value="active">已映射</option>
                        <option value="empty">待映射</option>
                    </select>
                </div>

                <!-- 操作按钮 -->
                <div class="form-control">
                    <label class="label py-1 md:py-2 md:opacity-0">
                        <span class="label-text text-sm md:text-base">　</span>
                    </label>
                    <div class="flex gap-2 flex-wrap">
                        <button type="button" class="btn btn-primary btn-sm md:btn-md flex-1 md:flex-none" id="add-mapping-btn">
                            <i class="bi bi-plus md:mr-2" aria-label="添加图标"></i>
                            添加映射
                        </button>
                        <button type="button" class="btn btn-outline btn-sm md:btn-md flex-1 md:flex-none" id="refresh-mapping-btn">
                            <i class="bi bi-arrow-clockwise md:mr-2" aria-label="刷新图标"></i>
                            刷新
                        </button>
                        <div class="dropdown dropdown-end">
                            <div tabindex="0" role="button" class="btn btn-outline btn-sm md:btn-md">
                                <i class="bi bi-three-dots-vertical" aria-label="更多操作图标"></i>
                            </div>
                            <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow border border-base-300">
                                <li>
                                    <a id="import-mapping-btn">
                                        <i class="bi bi-upload" aria-label="导入图标"></i>
                                        导入映射
                                    </a>
                                </li>
                                <li>
                                    <a id="export-mapping-btn">
                                        <i class="bi bi-download" aria-label="导出图标"></i>
                                        导出映射
                                    </a>
                                </li>
                                <li>
                                    <a id="batch-delete-mapping-btn" class="text-error">
                                        <i class="bi bi-trash" aria-label="批量删除图标"></i>
                                        批量删除
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 映射统计信息 -->
    <div class="stats shadow mb-6 w-full">
        <div class="stat">
            <div class="stat-figure text-primary">
                <i class="bi bi-arrow-left-right text-primary" style="font-size: 2rem;" aria-label="总映射图标"></i>
            </div>
            <div class="stat-title">总映射数</div>
            <div class="stat-value text-primary" id="total-mappings">0</div>
            <div class="stat-desc">当前类型的映射总数</div>
        </div>

        <div class="stat">
            <div class="stat-figure text-success">
                <i class="bi bi-arrow-right text-success" style="font-size: 2rem;" aria-label="转换映射图标"></i>
            </div>
            <div class="stat-title">转换映射</div>
            <div class="stat-value text-success" id="active-mappings">0</div>
            <div class="stat-desc">转换为其他值的规则</div>
        </div>

        <div class="stat">
            <div class="stat-figure text-error">
                <i class="bi bi-trash text-error" style="font-size: 2rem;" aria-label="删除规则图标"></i>
            </div>
            <div class="stat-title">删除规则</div>
            <div class="stat-value text-error" id="empty-mappings">0</div>
            <div class="stat-desc">删除原始值的规则</div>
        </div>

        <div class="stat">
            <div class="stat-figure text-info">
                <i class="bi bi-check-square text-info" style="font-size: 2rem;" aria-label="选中映射图标"></i>
            </div>
            <div class="stat-title">选中映射</div>
            <div class="stat-value text-info" id="selected-mappings">0</div>
            <div class="stat-desc">当前选中的映射数</div>
        </div>
    </div>

    <!-- 映射表格 -->
    <div class="card bg-base-100 shadow-lg border border-base-300">
        <div class="card-body p-0">
            <!-- 表格头部 -->
            <div class="flex items-center justify-between p-6 border-b border-base-300">
                <h3 class="text-xl font-semibold" id="mapping-table-title">标签映射列表</h3>
            </div>

            <!-- 表格内容 -->
            <div class="overflow-x-auto">
                <table class="table table-zebra">
                    <thead>
                        <tr>
                            <th class="w-12">
                                <input type="checkbox" class="checkbox checkbox-primary" id="mapping-select-all">
                            </th>
                            <th>原始值</th>
                            <th>映射值</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th class="w-32">操作</th>
                        </tr>
                    </thead>
                    <tbody id="mapping-table-body">
                        <!-- 映射数据将在这里动态加载 -->
                    </tbody>
                </table>
            </div>

            <!-- 表格底部 -->
            <div class="flex items-center justify-between p-6 border-t border-base-300">
                <div class="text-sm text-base-content/60" id="mapping-count-info">
                    共 0 个映射规则
                </div>
                <div id="mapping-pagination-container">
                    <!-- 分页将在这里生成 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 加载状态 -->
    <div class="flex flex-col items-center justify-center py-16 hidden" id="mapping-loading-state">
        <span class="loading loading-spinner loading-lg mb-4"></span>
        <p class="text-base-content/60">加载映射数据...</p>
    </div>

    <!-- 空状态 -->
    <div class="flex flex-col items-center justify-center py-16 hidden" id="mapping-empty-state">
        <div class="bg-base-200 rounded-full p-6 mb-6">
            <i class="bi bi-arrow-left-right text-base-content/40" style="font-size: 4rem;" aria-label="空映射图标"></i>
        </div>
        <h3 class="text-2xl font-bold mb-2 text-base-content">暂无映射规则</h3>
        <p class="text-base-content/60 mb-6 text-center max-w-md">您还没有创建任何映射规则，点击下方按钮创建第一个映射规则。</p>
        <button type="button" class="btn btn-primary btn-wide" id="add-first-mapping-btn">
            <i class="bi bi-plus mr-2" aria-label="添加图标"></i>
            创建第一个映射规则
        </button>
    </div>

    <!-- 错误状态 -->
    <div class="flex flex-col items-center justify-center py-16 hidden" id="mapping-error-state">
        <div class="bg-error/10 rounded-full p-6 mb-6">
            <i class="bi bi-exclamation-triangle text-error" style="font-size: 4rem;" aria-label="错误图标"></i>
        </div>
        <h3 class="text-2xl font-bold mb-2 text-base-content">加载失败</h3>
        <p class="text-base-content/60 mb-6 text-center max-w-md" id="mapping-error-message">加载映射规则时发生错误，请稍后重试。</p>
        <button type="button" class="btn btn-primary btn-wide" id="retry-mapping-btn">
            <i class="bi bi-arrow-clockwise mr-2" aria-label="重试图标"></i>
            重新加载
        </button>
    </div>
</div>

<!-- 常规设置面板 -->
<div id="general-panel" class="tab-panel hidden">
    <!-- 视频扩展名配置 -->
    <div class="card bg-base-100 shadow-lg border border-base-300 mb-6">
        <div class="card-body">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h3 class="card-title text-lg">
                        <i class="bi bi-file-earmark-play text-primary" aria-label="视频文件图标"></i>
                        视频文件扩展名
                    </h3>
                    <p class="text-sm text-base-content/60 mt-1">配置系统支持的视频文件格式</p>
                </div>
                <div class="flex gap-2">
                    <button type="button" class="btn btn-ghost btn-sm" id="reload-extensions-btn" title="刷新配置">
                        <i class="bi bi-arrow-repeat" aria-label="刷新图标"></i>
                    </button>
                </div>
            </div>

            <!-- 扩展名管理区域 -->
            <div class="space-y-4">
                <!-- 添加新扩展名 -->
                <div class="flex items-center gap-3 p-3 bg-base-200/50 rounded-lg">
                    <div class="flex items-center gap-2 flex-1">
                        <span class="text-sm font-medium">添加扩展名:</span>
                        <div class="join">
                            <input
                                type="text"
                                class="input input-bordered input-sm join-item w-24"
                                id="new-extension-input"
                                placeholder="mp4"
                                maxlength="10"
                            />
                            <button type="button" class="btn btn-primary btn-sm join-item" id="add-extension-btn">
                                <i class="bi bi-plus" aria-label="添加图标"></i>
                            </button>
                        </div>
                    </div>
                    <div class="text-xs text-base-content/60">
                        <i class="bi bi-info-circle mr-1" aria-label="提示图标"></i>
                        支持带点号或不带点号
                    </div>
                </div>

                <!-- 当前扩展名列表 -->
                <div>
                    <label class="label py-2">
                        <span class="label-text font-medium text-sm">当前支持的扩展名</span>
                        <span class="label-text-alt text-xs" id="extensions-count">0 个扩展名</span>
                    </label>
                    <div class="flex flex-wrap gap-2 p-4 bg-base-200 rounded-lg min-h-[4rem] max-h-40 overflow-y-auto" id="current-extensions-display">
                        <!-- 当前扩展名标签将在这里显示 -->
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- 其他常规设置 -->
    <div class="card bg-base-100 shadow-lg border border-base-300">
        <div class="card-body">
            <h3 class="card-title text-xl mb-6">
                <i class="bi bi-sliders text-primary" aria-label="其他设置图标"></i>
                其他设置
            </h3>
            <p class="text-base-content/60 mb-6">更多系统配置选项</p>

            <div class="alert alert-info">
                <i class="bi bi-info-circle" aria-label="信息图标"></i>
                <span>更多设置功能正在开发中，敬请期待...</span>
            </div>
        </div>
    </div>
</div>

<!-- 显示设置面板 -->
<div id="display-panel" class="tab-panel hidden">
    <div class="card bg-base-100 shadow-lg border border-base-300">
        <div class="card-body">
            <h3 class="card-title text-xl mb-6">
                <i class="bi bi-display text-primary" aria-label="显示设置图标"></i>
                显示设置
            </h3>
            <p class="text-base-content/60 mb-6">配置界面显示和主题相关设置</p>
            
            <div class="alert alert-info">
                <i class="bi bi-info-circle" aria-label="信息图标"></i>
                <span>显示设置功能正在开发中，敬请期待...</span>
            </div>
        </div>
    </div>
</div>

<!-- 关于系统面板 -->
<div id="about-panel" class="tab-panel hidden">
    <div class="card bg-base-100 shadow-lg border border-base-300">
        <div class="card-body">
            <h3 class="card-title text-xl mb-6">
                <i class="bi bi-info-circle text-primary" aria-label="关于图标"></i>
                关于系统
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-semibold mb-3">系统信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-base-content/70">系统名称:</span>
                            <span class="font-medium">媒体管理器</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">版本:</span>
                            <span class="font-medium">v0.9.4</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">框架:</span>
                            <span class="font-medium">FastAPI + DaisyUI</span>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h4 class="text-lg font-semibold mb-3">版权信息</h4>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-base-content/70">开发者:</span>
                            <span class="font-medium">KleinerSource</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">版权:</span>
                            <span class="font-medium">© 2025 KleinerSource</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-base-content/70">许可:</span>
                            <span class="font-medium">All rights reserved</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加/编辑映射模态框 -->
<dialog id="mapping-modal" class="modal">
    <div class="modal-box w-11/12 max-w-lg">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>
        <h3 class="font-bold text-xl mb-6" id="mapping-modal-title">添加映射规则</h3>

        <form id="mapping-form" class="space-y-6">
            <input type="hidden" id="mapping-id">
            <input type="hidden" id="mapping-type">

            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">原始值 <span class="text-error">*</span></span>
                </label>
                <input type="text" class="input input-bordered w-full" id="mapping-original-value" name="original_value" placeholder="输入原始值" required>
                <div class="label">
                    <span class="label-text-alt">导入时遇到此值将被映射为下方的映射值</span>
                </div>
                <div class="label">
                    <span class="label-text-alt text-error hidden" id="mapping-original-error"></span>
                </div>
            </div>

            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">映射值</span>
                </label>
                <input type="text" class="input input-bordered w-full" id="mapping-mapped-value" name="mapped_value" placeholder="输入映射值（留空表示删除）">
                <div class="label">
                    <span class="label-text-alt">留空表示在导入时删除此原始值</span>
                </div>
                <div class="label">
                    <span class="label-text-alt text-error hidden" id="mapping-mapped-error"></span>
                </div>
            </div>

            <!-- 验证警告 -->
            <div class="alert alert-warning hidden" id="mapping-validation-warning">
                <i class="bi bi-exclamation-triangle" aria-label="警告图标"></i>
                <span>原始值和映射值不能相同</span>
            </div>

            <div class="modal-action">
                <button type="button" class="btn btn-outline" onclick="document.getElementById('mapping-modal').close()">取消</button>
                <button type="submit" class="btn btn-primary" id="mapping-submit-btn">
                    <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                    保存
                </button>
            </div>
        </form>
    </div>
</dialog>

<!-- 删除映射确认模态框 -->
<dialog id="delete-mapping-modal" class="modal">
    <div class="modal-box w-11/12 max-w-lg">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">确认删除映射规则</h3>
        <p class="mb-4" id="delete-mapping-message">您确定要删除这个映射规则吗？</p>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('delete-mapping-modal').close()">取消</button>
            <button type="button" class="btn btn-error" id="confirm-delete-mapping-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                删除
            </button>
        </div>
    </div>
</dialog>

<!-- 批量删除映射确认模态框 -->
<dialog id="batch-delete-mapping-modal" class="modal">
    <div class="modal-box w-11/12 max-w-lg">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>

        <h3 class="font-bold text-xl mb-4">批量删除确认</h3>
        <p class="mb-4" id="batch-delete-mapping-message">您确定要删除选中的映射规则吗？</p>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('batch-delete-mapping-modal').close()">取消</button>
            <button type="button" class="btn btn-error" id="confirm-batch-delete-mapping-btn">
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                删除全部
            </button>
        </div>
    </div>
</dialog>

<!-- 导入映射模态框 -->
<dialog id="import-mapping-modal" class="modal">
    <div class="modal-box w-11/12 max-w-2xl">
        <form method="dialog">
            <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2">✕</button>
        </form>
        <h3 class="font-bold text-xl mb-6">导入映射规则</h3>

        <div class="space-y-6">
            <!-- 文件上传 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">选择JSON文件</span>
                </label>
                <input type="file" class="file-input file-input-bordered w-full" id="mapping-file-input" accept=".json">
                <div class="label">
                    <span class="label-text-alt">支持JSON格式的映射规则文件</span>
                </div>
            </div>

            <!-- 导入选项 -->
            <div class="form-control">
                <label class="label cursor-pointer justify-start gap-3">
                    <input type="checkbox" class="checkbox checkbox-primary" id="overwrite-mapping-checkbox">
                    <span class="label-text">覆盖已存在的映射规则</span>
                </label>
                <div class="label">
                    <span class="label-text-alt">勾选此项将覆盖已存在的同名映射规则</span>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="form-control">
                <label class="label">
                    <span class="label-text font-medium">文件预览</span>
                </label>
                <textarea class="textarea textarea-bordered h-32" id="mapping-preview-textarea" placeholder="选择文件后将显示内容预览..." readonly></textarea>
            </div>
        </div>

        <div class="modal-action">
            <button type="button" class="btn btn-outline" onclick="document.getElementById('import-mapping-modal').close()">取消</button>
            <button type="button" class="btn btn-primary" id="import-mapping-submit-btn" disabled>
                <span class="loading loading-spinner loading-sm mr-2 hidden"></span>
                导入
            </button>
        </div>
    </div>
</dialog>
{% endblock %}

{% block extra_js %}
<script src="/static/js/settings.js?=v0.9.4"></script>
{% endblock %}
